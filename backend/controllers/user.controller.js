const authService = require('../services/auth.service');
const db = require('../config/database'); 
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const { getRandomDefaultAvatar, getDefaultAvatarById, DEFAULT_AVATARS } = require('../constants/defaultAvatars');

/**
 * Get a consistent default avatar based on user ID
 * @param {number} userId - User ID
 * @returns {Object} Avatar object with url, filename, and name
 */
const getConsistentDefaultAvatar = (userId) => {
  if (!userId) return getRandomDefaultAvatar();

  // Use user ID to consistently select the same avatar
  const avatarIndex = userId % DEFAULT_AVATARS.length;
  return DEFAULT_AVATARS[avatarIndex];
};

// Keep track of processed codes to prevent duplicates
const processedCodes = new Set();

const userController = {
    register: async (req, res) => {
        try {
            const { username, email, password, displayName } = req.body;
            
            if (!username || !email || !password) {
                return res.status(400).json({ message: 'Please provide username, email, and password' });
            }
            
            const result = await authService.register({
                username,
                email,
                password,
                displayName: displayName || username
            });
            
            res.status(201).json({
                message: 'User registered successfully',
                user: result.user,
                token: result.token
            });
        } catch (error) {
            console.error('Registration error:', error);
            
            if (error.message.includes('already')) {
                return res.status(409).json({ message: error.message });
            }
            
            res.status(500).json({ message: error.message || 'Error registering user' });
        }
    },

    login: async (req, res) => {
        try {
            const { email, password } = req.body;
            
            if (!email || !password) {
                return res.status(400).json({ message: 'Please provide email and password' });
            }
            
            const result = await authService.login(email, password);
            
            res.status(200).json({
                message: 'Login successful',
                user: result.user,
                token: result.token
            });
        } catch (error) {
            console.error('Login error:', error);
            
            if (error.message === 'User not found' || error.message === 'Invalid password') {
                return res.status(401).json({ message: 'Invalid email or password' });
            }
            
            if (error.message.includes('Please login with')) {
                return res.status(400).json({ message: error.message });
            }
            
            res.status(500).json({ message: 'Server error during login' });
        }
    },

    googleLogin: async (req, res) => {
        try {
            const { token, tokenType } = req.body;
            
            if (!token) {
                return res.status(400).json({ message: 'No token provided' });
            }
            
            const result = await authService.googleLogin(token, tokenType || 'id_token');
            
            res.status(200).json({
                message: 'Google login successful',
                user: result.user,
                token: result.token
            });
        } catch (error) {
            console.error('Google login error:', error);
            res.status(500).json({ message: 'Server error during authentication' });
        }
    },

    discordCallback: async (req, res) => {
        try {
            const { code, redirectUri } = req.body;
            
            if (!code) {
                return res.status(400).json({ message: 'No authorization code provided' });
            }
            
            // Simple mechanism to prevent duplicate processing of the same code
            const codeSignature = `${code.substring(0, 10)}`;
            
            if (processedCodes.has(codeSignature)) {
                return res.status(400).json({ message: 'This authorization code has already been used' });
            }
            
            // Track this code to prevent reuse
            processedCodes.add(codeSignature);
            
            // Set up automatic cleanup of old codes (after 5 minutes)
            setTimeout(() => {
                processedCodes.delete(codeSignature);
            }, 5 * 60 * 1000);
            
            const result = await authService.discordLogin(code, redirectUri);
            
            if (result.success) {
                res.status(200).json({
                    message: 'Discord login successful',
                    user: result.user,
                    token: result.token
                });
            } else {
                res.status(400).json({ message: result.message });
            }
        } catch (error) {
            console.error('Discord login error:', error);
            res.status(500).json({ message: 'Server error during Discord authentication' });
        }
    },

    logout: async (req, res) => {
        try {
            // JWT is stateless so we just tell the client to remove the token
            res.status(200).json({ message: 'Logout successful' });
        } catch (error) {
            res.status(500).json({ message: error.message });
        }
    },

    getProfile: async (req, res) => {
        try {
            const userId = req.user.userId || req.user.id;
            
            // Get user profile from database using Knex
            const user = await db('users')
                .select('id', 'username', 'email', 'bio', 'profile_image', 'display_name', 
                        'role', 'created_at', 'referral_code', 'credits')
                .where('id', userId)
                .first();
            
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }
            
            // Format dates and remove sensitive fields
            const formattedUser = {
                ...user,
                profileImage: user.profile_image,
                displayName: user.display_name,
                createdAt: new Date(user.created_at).toISOString(),
                joinDate: new Date(user.created_at).toISOString(),
                referralCode: user.referral_code
            };
            
            // Remove snake_case versions and sensitive data
            delete formattedUser.profile_image;
            delete formattedUser.display_name;
            delete formattedUser.created_at;
            delete formattedUser.referral_code;
            
            res.json(formattedUser);
        } catch (error) {
            console.error('Error fetching user profile:', error);
            res.status(500).json({ message: 'Error fetching profile' });
        }
    },

    updateProfile: async (req, res) => {
        try {
            const userId = req.user?.userId || req.user?.id;
            const { username, bio } = req.body;
            
            if (!userId) {
                return res.status(401).json({ message: 'Authentication required' });
            }
            
            // Validation
            if (!username || username.trim().length < 3) {
                return res.status(400).json({ 
                    message: 'Username must be at least 3 characters' 
                });
            }
            
            try {
                // Check if user exists
                const dbUser = await db('users').where('id', userId).first();
                
                if (!dbUser) {
                    return res.status(404).json({ message: 'User not found' });
                }
                
                // Check if username is already taken
                const existingUser = await db('users')
                    .where('username', username.toLowerCase())
                    .where('id', '!=', userId)
                    .first();
                
                if (existingUser) {
                    return res.status(400).json({ message: 'Username already taken' });
                }
                
                // Update the user profile
                await db('users')
                    .where('id', userId)
                    .update({ 
                        username: username.toLowerCase(), 
                        bio: bio || null,
                        updated_at: new Date()
                    });
                
                // Get updated user data
                const updatedUser = await db('users')
                    .select('id', 'username', 'email', 'bio', 'profile_image', 'display_name', 'role')
                    .where('id', userId)
                    .first();
                
                if (!updatedUser) {
                    return res.status(500).json({ message: 'Failed to update profile' });
                }
                
                // Format response
                const formattedUser = {
                    ...updatedUser,
                    profileImage: updatedUser.profile_image,
                    displayName: updatedUser.display_name
                };
                
                delete formattedUser.profile_image;
                delete formattedUser.display_name;
              
                // Send successful response
                return res.json({
                    message: 'Profile updated successfully',
                    user: formattedUser
                });
            } catch (dbError) {
                console.error('Database error during profile update:', dbError);
                return res.status(500).json({ 
                    message: 'Database error during profile update',
                    error: dbError.message
                });
            }
        } catch (error) {
            console.error('General error in updateProfile:', error);
            return res.status(500).json({ 
                message: 'Server error in updateProfile',
                error: error.message
            });
        }
    },

    getPublicProfile: async (req, res) => {
        try {
            const { userId } = req.params;
            
            // Get user profile using Knex
            const user = await db('users')
                .select('id', 'username', 'profile_image', 'display_name', 'bio', 'role', 'created_at')
                .where('id', userId)
                .first();
            
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }
            
            // Get game count (you may need to adjust this based on your actual tables)
            const gameCountResult = await db('games')
                .where('user_id', userId)
                .count('* as count')
                .first();
            
            // Get review count
            const reviewCountResult = await db('reviews')
                .where('user_id', userId)
                .count('* as count')
                .first();
            
            // Format response
            const publicProfile = {
                id: user.id,
                username: user.username,
                profileImage: user.profile_image,
                displayName: user.display_name,
                bio: user.bio,
                role: user.role,
                joinDate: new Date(user.created_at).toISOString().split('T')[0],
                gameCount: parseInt(gameCountResult.count),
                reviewCount: parseInt(reviewCountResult.count)
            };
            
            res.json(publicProfile);
        } catch (error) {
            console.error('Error fetching public profile:', error);
            res.status(500).json({ message: 'Error fetching profile' });
        }
    },

    getUserReviews: async (req, res) => {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 10 } = req.query;
            const offset = (page - 1) * limit;
            
            // Check if user exists
            const user = await db('users').where('id', userId).first();
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }
            
            // Get reviews with game information
            const reviews = await db('reviews as r')
                .select(
                    'r.id', 'r.title', 'r.comment', 'r.rating', 'r.created_at',
                    'g.id as game_id', 'g.title as game_title'
                )
                .join('games as g', 'r.game_id', 'g.id')
                .where('r.user_id', userId)
                .orderBy('r.created_at', 'desc')
                .limit(parseInt(limit))
                .offset(offset);
            
            // Get total count for pagination
            const [{ total }] = await db('reviews')
                .where('user_id', userId)
                .count('* as total');
            
            const totalPages = Math.ceil(total / limit);
            
            res.json({
                reviews,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages,
                    totalReviews: parseInt(total),
                    limit: parseInt(limit)
                }
            });
        } catch (error) {
            console.error('Error fetching user reviews:', error);
            res.status(500).json({ message: 'Error fetching reviews' });
        }
    },

    getUserPosts: async (req, res) => {
        try {
            const { userId } = req.params;
            
            // Check if user exists
            const user = await User.findByPk(userId);
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }
            
            // Get community posts
            const [posts] = await db.sequelize.query(`
                SELECT p.id, p.title, p.content, p.image,
                    SUBSTRING(p.content, 1, 200) as excerpt,
                    DATE_FORMAT(p.createdAt, '%M %d, %Y') as date,
                    p.likesCount, COUNT(c.id) as commentCount
                FROM Posts p
                LEFT JOIN Comments c ON p.id = c.postId
                WHERE p.userId = ?
                GROUP BY p.id
                ORDER BY p.createdAt DESC
            `, { 
                replacements: [userId] 
            });
            
            res.status(200).json(posts);
        } catch (error) {
            console.error('Error getting user posts:', error);
            res.status(500).json({ message: 'Server error' });
        }
    },

    /**
     * Select avatar from predefined options
     */
    selectAvatar: async (req, res) => {
        try {
            const userId = req.user?.userId || req.user?.id;
            const { avatarId, avatarUrl } = req.body;
            
            if (!userId) {
                return res.status(401).json({ message: 'Authentication required' });
            }

            if (!avatarId || !avatarUrl) {
                return res.status(400).json({ message: 'Avatar ID and URL are required' });
            }

            // Validate that the avatar is from our allowed list
            const { getDefaultAvatarById } = require('../constants/defaultAvatars');
            const validAvatar = getDefaultAvatarById(avatarId);
            
            if (!validAvatar || validAvatar.url !== avatarUrl) {
                return res.status(400).json({ message: 'Invalid avatar selection' });
            }

            // Update user's profile image in database
            await db('users')
                .where('id', userId)
                .update({ 
                    profile_image: avatarUrl,
                    updated_at: new Date()
                });

            // Get updated user data
            const updatedUser = await db('users')
                .select('id', 'username', 'email', 'profile_image', 'display_name', 'role')
                .where('id', userId)
                .first();

            res.json({
                message: 'Avatar updated successfully',
                user: {
                    id: updatedUser.id,
                    username: updatedUser.username,
                    email: updatedUser.email,
                    profileImage: updatedUser.profile_image,
                    displayName: updatedUser.display_name,
                    role: updatedUser.role
                }
            });
        } catch (error) {
            console.error('Error selecting avatar:', error);
            res.status(500).json({ message: 'Error updating avatar' });
        }
    },

    /**
     * Get available avatars
     */
    getAvailableAvatars: async (req, res) => {
        try {
            const { getAllDefaultAvatars } = require('../constants/defaultAvatars');
            const avatars = getAllDefaultAvatars();

            res.json({
                message: 'Available avatars retrieved successfully',
                avatars
            });
        } catch (error) {
            console.error('Error getting available avatars:', error);
            res.status(500).json({ message: 'Error retrieving avatars' });
        }
    },

    /**
     * Migrate all users to use consistent default avatars
     * This replaces any social media profile images with our default avatars
     */
    migrateToDefaultAvatars: async (req, res) => {
        try {
            // Get all users
            const users = await db('users').select('id', 'profile_image');

            let updatedCount = 0;

            for (const user of users) {
                // Check if user has a social media profile image or no avatar
                const needsUpdate = !user.profile_image ||
                    (!user.profile_image.includes('/avatar/1.png') &&
                     !user.profile_image.includes('/avatar/2.png'));

                if (needsUpdate) {
                    const defaultAvatar = getConsistentDefaultAvatar(user.id);
                    await db('users')
                        .where('id', user.id)
                        .update({ profile_image: defaultAvatar.url });
                    updatedCount++;
                }
            }

            res.json({
                message: `Successfully migrated ${updatedCount} users to default avatars`,
                updatedCount,
                totalUsers: users.length
            });
        } catch (error) {
            console.error('Error migrating users to default avatars:', error);
            res.status(500).json({ message: 'Error migrating avatars' });
        }
    },

    getRecentlyPlayed: async (req, res) => {
        try {
            const userId = req.user.userId || req.user.id;
            console.log('Fetching recently played games for user:', userId);

            // Get recently played games from user_games table with images
            const recentlyPlayed = await db('user_games')
                .join('games', 'user_games.game_id', 'games.id')
                .leftJoin('game_images as card_img', function() {
                    this.on('games.id', '=', 'card_img.game_id')
                        .andOn('card_img.image_type', '=', db.raw('?', ['card']));
                })
                .leftJoin('game_images as gif_img', function() {
                    this.on('games.id', '=', 'gif_img.game_id')
                        .andOn('gif_img.image_type', '=', db.raw('?', ['gif']));
                })
                .select(
                    'games.id',
                    'games.title',
                    'games.description',
                    'games.genre',
                    'games.slug',
                    'games.tags',
                    'games.price_model as priceModel',
                    'games.price',
                    'games.credit_price as creditPrice',
                    'card_img.file_path as cardImage',
                    'gif_img.file_path as animationGif',
                    'games.release_date as releaseDate',
                    'user_games.last_played_at as lastPlayedAt'
                )
                .where('user_games.user_id', userId)
                .whereNotNull('user_games.last_played_at')
                .orderBy('user_games.last_played_at', 'desc')
                .limit(10);

            console.log('Found recently played games:', recentlyPlayed.length);

            // Format the games data
            const formattedGames = recentlyPlayed.map(game => ({
                id: game.id,
                title: game.title,
                description: game.description,
                slug: game.slug,
                image: game.cardImage,
                hoverGif: game.animationGif,
                genre: game.genre,
                tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
                paymentType: game.priceModel,
                price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` :
                       game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
                releaseDate: game.releaseDate,
                lastPlayedAt: game.lastPlayedAt
            }));

            res.json({ games: formattedGames });
        } catch (error) {
            console.error('Error fetching recently played games:', error);
            res.status(500).json({ message: 'Error fetching recently played games' });
        }
    },

    getFavorites: async (req, res) => {
        try {
            const userId = req.user.userId || req.user.id;
            console.log('Fetching favorites for user:', userId);

            // Get favorite games from user_games table with images
            const favorites = await db('user_games')
                .join('games', 'user_games.game_id', 'games.id')
                .leftJoin('game_images as card_img', function() {
                    this.on('games.id', '=', 'card_img.game_id')
                        .andOn('card_img.image_type', '=', db.raw('?', ['card']));
                })
                .leftJoin('game_images as gif_img', function() {
                    this.on('games.id', '=', 'gif_img.game_id')
                        .andOn('gif_img.image_type', '=', db.raw('?', ['gif']));
                })
                .select(
                    'games.id',
                    'games.title',
                    'games.description',
                    'games.genre',
                    'games.slug',
                    'games.tags',
                    'games.price_model as priceModel',
                    'games.price',
                    'games.credit_price as creditPrice',
                    'card_img.file_path as cardImage',
                    'gif_img.file_path as animationGif',
                    'games.release_date as releaseDate',
                    'user_games.created_at as favoritedAt'
                )
                .where('user_games.user_id', userId)
                .where('user_games.is_favorite', true)
                .orderBy('user_games.created_at', 'desc');

            console.log('Found favorites:', favorites.length);

            // Format the games data
            const formattedGames = favorites.map(game => ({
                id: game.id,
                title: game.title,
                description: game.description,
                slug: game.slug,
                image: game.cardImage,
                hoverGif: game.animationGif,
                genre: game.genre,
                tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
                paymentType: game.priceModel,
                price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` :
                       game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
                releaseDate: game.releaseDate,
                favoritedAt: game.favoritedAt
            }));

            res.json({ favorites: formattedGames });
        } catch (error) {
            console.error('Error fetching favorite games:', error);
            res.status(500).json({ message: 'Error fetching favorite games' });
        }
    },

    debugUserGames: async (req, res) => {
        try {
            const userId = req.user.userId || req.user.id;
            console.log('Debug: Checking user_games for user:', userId);

            // Get all user_games entries for this user
            const userGames = await db('user_games')
                .where('user_id', userId)
                .select('*');

            console.log('Debug: Found user_games entries:', userGames);

            // Also check table structure
            const tableInfo = await db.raw('DESCRIBE user_games');
            console.log('Debug: user_games table structure:', tableInfo[0]);

            res.json({
                userGames,
                tableStructure: tableInfo[0],
                userId
            });
        } catch (error) {
            console.error('Error in debug endpoint:', error);
            res.status(500).json({ message: 'Debug error', error: error.message });
        }
    }
};

module.exports = userController;