import { Link } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaEnvelope } from 'react-icons/fa';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';

// X (Twitter) SVG Icon Component
const XIcon = () => (
  <svg viewBox="0 0 24 24" className="w-6 h-6 fill-current">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { createLanguageLink } = useLanguageNavigation();

  return (
    <footer className="bg-[#151515] text-[#ccc] pt-[50px] px-5 pb-5 mt-auto">
      <div className="max-w-[1200px] mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-[30px]">
        <div className="mb-5">
          <h3 className="text-white text-[1.2rem] mb-[15px] pb-[10px] relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-gradient-to-r after:from-[#f44336] after:to-[#ff9800]">
            IndieRepo
          </h3>
          <p className="leading-[1.6] mb-[15px] text-[0.9rem]">
            Discover and support the best indie games from developers around the world.
          </p>
        </div>

        <div className="mb-5">
          <h3 className="text-white text-[1.2rem] mb-[15px] pb-[10px] relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-gradient-to-r after:from-[#f44336] after:to-[#ff9800]">
            Navigation
          </h3>
          <ul className="list-none p-0 m-0">
            <li className="mb-2">
              <Link to={createLanguageLink('/')} className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                Home
              </Link>
            </li>
            <li className="mb-2">
              <Link to={createLanguageLink('/browse')} className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                Browse Games
              </Link>
            </li>
            <li className="mb-2">
              <Link to="/search" className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                Search
              </Link>
            </li>
            <li className="mb-2">
              <Link to="/about" className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                About Us
              </Link>
            </li>
          </ul>
        </div>

        <div className="mb-5">
          <h3 className="text-white text-[1.2rem] mb-[15px] pb-[10px] relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-gradient-to-r after:from-[#f44336] after:to-[#ff9800]">
            Legal
          </h3>
          <ul className="list-none p-0 m-0">
            <li className="mb-2">
              <Link to={createLanguageLink('/terms')} className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                Terms of Service
              </Link>
            </li>
            <li className="mb-2">
              <Link to={createLanguageLink('/privacy')} className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                Privacy Policy
              </Link>
            </li>
            <li className="mb-2">
              <Link to="/cookies" className="text-[#aaa] no-underline transition-colors duration-300 text-[0.9rem] hover:text-[#f44336]">
                Cookie Policy
              </Link>
            </li>
          </ul>
        </div>

        <div className="mb-5">
          <h3 className="text-white text-[1.2rem] mb-[15px] pb-[10px] relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-10 after:h-0.5 after:bg-gradient-to-r after:from-[#f44336] after:to-[#ff9800]">
            Connect With Us
          </h3>
          <div className="flex gap-[15px]">
            <a
              href="https://x.com/IndieRepo"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#aaa] text-[1.5rem] transition-all duration-300 inline-flex hover:text-[#f44336] hover:-translate-y-[3px]"
            >
              <XIcon />
            </a>
            <a
              href="https://discord.gg/IndieRepo"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#aaa] text-[1.5rem] transition-all duration-300 inline-flex hover:text-[#f44336] hover:-translate-y-[3px]"
            >
              <FaDiscord />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="text-[#aaa] text-[1.5rem] transition-all duration-300 inline-flex hover:text-[#f44336] hover:-translate-y-[3px]"
            >
              <FaEnvelope />
            </a>
          </div>
        </div>
      </div>

      <div className="max-w-[1200px] mx-auto pt-5 mt-[30px] border-t border-[#333] flex justify-between items-center flex-wrap text-[0.85rem] text-[#888]">
        <p>&copy; {currentYear} IndieRepo. All rights reserved.</p>
        <p className="italic">Made with ❤️ for indie game enthusiasts</p>
      </div>
    </footer>
  );
};

export default Footer;
