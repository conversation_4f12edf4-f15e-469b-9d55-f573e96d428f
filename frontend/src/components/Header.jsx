import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { FaBars, FaTimes, FaUserCircle, FaHome, FaInfoCircle, FaSignOutAlt, FaUpload, FaUser, FaChevronDown, FaSearch } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { useLanguage } from '../context/LanguageContext';
import { useSidebar } from '../context/SidebarContext';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';
import LanguageSelector from './LanguageSelector';
import SearchAutocomplete from './SearchAutocomplete';
import { getAvatarUrl } from '../utils/mediaUtils';

/**
 * Header component with responsive design for both desktop and mobile views
 */
const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);
  const { user, logout } = useAuth();
  const { t } = useLanguage();
  const { isSidebarOpen, toggleSidebar } = useSidebar();
  const { navigate, createLanguageLink } = useLanguageNavigation();
  const userDropdownRef = useRef(null);

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMobileMenuOpen(false);
    setIsUserDropdownOpen(false);
  };

  const toggleUserDropdown = () => {
    setIsUserDropdownOpen(!isUserDropdownOpen);
  };

  // Close mobile menu and user dropdown when clicking outside or on escape
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false);
        setIsUserDropdownOpen(false);
        setIsMobileSearchOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  // Handle click outside for user dropdown only
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (isUserDropdownOpen && userDropdownRef.current && !userDropdownRef.current.contains(e.target)) {
        setIsUserDropdownOpen(false);
      }
    };

    if (isUserDropdownOpen) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => document.removeEventListener('click', handleClickOutside);
  }, [isUserDropdownOpen]);

  // Handle mobile menu overlay click
  const handleMobileMenuOverlayClick = () => {
    setIsMobileMenuOpen(false);
  };

  // Handle mobile search click outside (only when search is open)
  useEffect(() => {
    const handleSearchClickOutside = (e) => {
      // Only close if clicking outside both the search box and the search button
      if (!e.target.closest('.mobile-search') && !e.target.closest('.mobile-search-button')) {
        setIsMobileSearchOpen(false);
      }
    };

    if (isMobileSearchOpen) {
      // Add a small delay to prevent immediate closing when opening
      const timer = setTimeout(() => {
        document.addEventListener('click', handleSearchClickOutside);
      }, 100);

      return () => {
        clearTimeout(timer);
        document.removeEventListener('click', handleSearchClickOutside);
      };
    }
  }, [isMobileSearchOpen]);

  return (
    <header className="w-full bg-[#1e2030] sticky top-0 z-[1000] shadow-[0_2px_10px_rgba(0,0,0,0.3)]">
      {/* Main header container */}
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Top row - Sidebar Toggle, Logo, Desktop Nav, Mobile Menu Button */}
        <div className="flex items-center justify-between h-16">
          {/* Left side - Sidebar Toggle + Logo */}
          <div className="flex items-center space-x-4">
            {/* Sidebar Toggle Button */}
            <button
              onClick={toggleSidebar}
              className="text-white hover:text-gray-300 hover:bg-gray-800 rounded-lg transition-all duration-300"
              aria-label={isSidebarOpen ? t('sidebar.toggle.close') : t('sidebar.toggle.open')}
            >
              {isSidebarOpen ? <FaTimes className="text-xl" /> : <FaBars className="text-xl" />}
            </button>

            {/* Logo */}
            <Link
              to={createLanguageLink('/')}
              className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-[#f44336] to-[#ff9800] bg-clip-text text-transparent hover:scale-105 transition-transform duration-300"
            >
              {t('header.brand')}
            </Link>
          </div>

          {/* Desktop Search */}
          <div className="hidden md:flex items-center flex-1 max-w-md mx-4">
            <SearchAutocomplete className="w-full" />
          </div>

          {/* Desktop User Actions */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Language Selector */}
                <LanguageSelector />
            {user ? (
              <>
                {/* Upload Game Button */}
                <Link
                  to={createLanguageLink('/upload-game')}
                  className="flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-[#7b52ff] to-[#9b7dff] text-white font-semibold shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all duration-300"
                >
                  <FaUpload className="mr-2 text-sm" />
                  <span>{t('header.uploadGame')}</span>
                </Link>
                {/* User Dropdown */}
                <div className="relative" ref={userDropdownRef}>
                  <button
                    onClick={toggleUserDropdown}
                    className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-800 transition-colors duration-300"
                    aria-label="User menu"
                  >
                    {/* User Avatar */}
                    <img
                      src={getAvatarUrl(user.profileImage, user.id)}
                      alt={user.username || user.email}
                      className="w-8 h-8 rounded-full object-cover border-2 border-gray-600"
                      onError={(e) => {
                        // Fallback to user icon if image fails to load
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <FaUserCircle className="w-8 h-8 text-gray-400 hidden" />
                    <FaChevronDown className={`text-gray-400 text-sm transition-transform duration-200 ${isUserDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Dropdown Menu */}
                  {isUserDropdownOpen && (
                    <div className="absolute right-0 mt-2 min-w-[14rem] max-w-[24rem] bg-[#1a1a1a] border border-gray-700 rounded-lg shadow-xl z-50">
                      <div className="py-2">
                        {/* User Info */}
                        <div className="px-4 py-3 border-b border-gray-700">
                          <div className="flex items-center space-x-3">
                            <img
                              src={getAvatarUrl(user.profileImage, user.id)}
                              alt={user.username || user.email}
                              className="w-10 h-10 rounded-full object-cover border-2 border-gray-600 flex-shrink-0"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                            <FaUserCircle className="w-10 h-10 text-gray-400 hidden flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-white truncate" title={user.username || user.email}>{user.username || user.email}</p>
                              <p className="text-xs text-gray-400 truncate" title={user.email}>{user.email}</p>
                            </div>
                          </div>
                        </div>

                        {/* Profile Link */}
                        <Link
                          to={createLanguageLink('/profile')}
                          className="flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200"
                          onClick={() => setIsUserDropdownOpen(false)}
                        >
                          <FaUser className="mr-3 text-sm" />
                          <span>{t('header.profile')}</span>
                        </Link>

                        {/* Logout Button */}
                        <button
                          onClick={handleLogout}
                          className="w-full flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200 text-left"
                        >
                          <FaSignOutAlt className="mr-3 text-sm" />
                          <span>{t('header.logout')}</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                {/* Login Button */}
                <Link
                  to={createLanguageLink('/login')}
                  className="flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-[#4a65ff] to-[#5e3bff] text-white font-semibold shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all duration-300"
                >
                  <FaUserCircle className="mr-2" />
                  <span>{t('header.login')}</span>
                </Link>
              </>
            )}
          </div>

          {/* Mobile Icons */}
          <div className="md:hidden flex items-center space-x-3">
            {/* Mobile Search Icon */}
            <button
              onClick={() => setIsMobileSearchOpen(!isMobileSearchOpen)}
              className="mobile-search-button p-2 text-white hover:text-gray-300 hover:bg-gray-800 rounded-lg transition-all duration-300"
              aria-label={isMobileSearchOpen ? t('search.close') : t('search.open')}
            >
              {isMobileSearchOpen ? <FaTimes className="text-xl" /> : <FaSearch className="text-xl" />}
            </button>

            {/* Language Selector */}
            <LanguageSelector />

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="mobile-menu-button text-white hover:text-gray-300 hover:bg-gray-800 rounded-lg transition-all duration-300"
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? <FaTimes className="text-xl" /> : <FaBars className="text-xl" />}
            </button>
          </div>
        </div>

        {/* Mobile Search - Show below header on mobile when search is open */}
        {isMobileSearchOpen && (
          <div className="mobile-search md:hidden pb-4 pt-2 border-t border-gray-700">
            <SearchAutocomplete className="w-full" />
          </div>
        )}
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={handleMobileMenuOverlayClick} />
      )}

      {/* Mobile Menu */}
      <div className={`mobile-menu fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-[#151515] transform transition-transform duration-300 ease-in-out z-50 md:hidden ${
        isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Mobile Menu Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">Menu</h2>
          <button 
            onClick={() => setIsMobileMenuOpen(false)}
            className="p-2 text-white hover:text-gray-300 transition-colors duration-300"
            aria-label="Close menu"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Mobile Menu Content */}
        <nav className="p-4 space-y-2">
          {/* User Info for Mobile - Show when logged in */}
          {user && (
            <div className="px-4 py-3 mb-4 bg-gray-800 rounded-lg border border-gray-700">
              <div className="flex items-center space-x-3">
                <img
                  src={getAvatarUrl(user.profileImage, user.id)}
                  alt={user.username || user.email}
                  className="w-12 h-12 rounded-full object-cover border-2 border-gray-600 flex-shrink-0"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <FaUserCircle className="w-12 h-12 text-gray-400 hidden flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate" title={user.username || user.email}>{user.username || user.email}</p>
                  <p className="text-xs text-gray-400 truncate" title={user.email}>{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Home */}
          <Link
            to={createLanguageLink('/')}
            className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <FaHome className="text-lg" />
            <span>{t('header.home')}</span>
          </Link>

          {/* Upload Game */}
          <Link
            to={createLanguageLink('/upload-game')}
            className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <FaUpload className="text-lg" />
            <span>{t('header.uploadGame')}</span>
          </Link>

          {/* User-specific menu items */}
          {user && (
            <>
              {/* Profile */}
              <Link
                to={createLanguageLink('/profile')}
                className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <div className="w-5 h-5 rounded-full overflow-hidden border border-gray-600 flex-shrink-0">
                  <img
                    src={getAvatarUrl(user.profileImage, user.id)}
                    alt={user.username || user.email}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                  <FaUser className="w-full h-full text-gray-400 hidden" />
                </div>
                <span>{t('header.profile')}</span>
              </Link>

              {/* Logout */}
              <button
                onClick={handleLogout}
                className="w-full flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300 text-left"
              >
                <FaSignOutAlt className="text-lg" />
                <span>{t('header.logout')}</span>
              </button>
            </>
          )}

          {/* About */}
          <Link
            to={createLanguageLink('/about')}
            className="flex items-center space-x-3 px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <FaInfoCircle className="text-lg" />
            <span>{t('header.about')}</span>
          </Link>

          {/* Language Selector for Mobile */}
          <div className="px-4 py-3">
            <LanguageSelector />
          </div>

          {/* Authentication for non-logged in users */}
          {!user && (
            <Link
              to={createLanguageLink('/login')}
              className="flex items-center space-x-3 px-4 py-3 bg-gradient-to-r from-[#4a65ff] to-[#5e3bff] text-white hover:from-[#3a55ff] hover:to-[#4e2bff] rounded-lg transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <FaUserCircle className="text-lg" />
              <span>{t('header.login')}</span>
            </Link>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Header;
